# Third Party API Documentation Setup

This document describes the isolated Swagger documentation setup for third-party API endpoints in the Smart Eye API project.

## Overview

The Smart Eye API now provides isolated Swagger documentation specifically for third-party integrations. This allows external partners and developers to access comprehensive API documentation that includes only the endpoints relevant to third-party usage, without exposing internal API endpoints.

## Features

-   **Isolated Documentation**: Only third-party endpoints are included
-   **Organized by Categories**: Endpoints are grouped by functionality (Authentication, Company, User, Sites, Tanks, etc.)
-   **Multiple Formats**: Available in both Swagger UI and ReDoc formats
-   **OpenAPI 3.0 Schema**: Full OpenAPI 3.0 compliant schema generation
-   **Automatic Tagging**: Endpoints are automatically tagged and organized

## Access URLs

### Swagger UI (Interactive Documentation)

```
http://your-domain.com/api/v1/third-party/docs/
```

### ReDoc (Alternative Documentation Format)

```
http://your-domain.com/api/v1/third-party/redoc/
```

### Raw OpenAPI Schema (JSON/YAML)

```
http://your-domain.com/api/v1/third-party/third-party-schema/
```

## Available Endpoints

The isolated documentation includes the following third-party endpoints:

### Smart Tank Module

-   `POST /api/v1/third-party/login/` - Third Party User Login
-   `GET /api/v1/third-party/company-details/` - Get Company Details
-   `GET /api/v1/third-party/user-details/` - Get User Details
-   `GET /api/v1/third-party/site-details/` - Get Site Details
-   `GET /api/v1/third-party/site-tanks/{site_id}/` - Get Site Tanks

### Example/Testing

-   `GET /api/v1/third-party/example/` - Third Party Example Endpoint

**Note**: All endpoints are at the root third-party level but are grouped by module in the Swagger documentation for better organization.

## Implementation Details

### Architecture

The isolated documentation system consists of several key components:

1. **ThirdPartySchemaGenerator** (`thirdparty/schema.py`)

    - Custom schema generator that filters endpoints
    - Automatically tags endpoints for organization
    - Provides third-party specific configuration

2. **ThirdPartySpectacularAPIView** (`thirdparty/views.py`)

    - Custom API view that generates the OpenAPI schema
    - Uses the ThirdPartySchemaGenerator
    - Serves JSON/YAML schema formats

3. **URL Configuration** (`thirdparty/urls.py`)
    - Routes for documentation endpoints
    - Integration with drf-spectacular views

### Schema Generation Process

1. The system scans all URL patterns from third-party apps
2. Filters endpoints based on URL patterns and tags
3. Automatically organizes endpoints into logical groups
4. Generates OpenAPI 3.0 compliant schema
5. Serves documentation through Swagger UI and ReDoc

### Endpoint Tagging

All third-party endpoints are automatically tagged with categories:

-   `Third Party - Authentication`
-   `Third Party - Company`
-   `Third Party - User`
-   `Third Party - Sites`
-   `Third Party - Tanks`
-   `Third Party - Example`

## Configuration

### Adding New Third-Party Modules

To include new third-party modules in the documentation:

1. Import the module views and add endpoints directly to `thirdparty/urls.py`:

```python
from .smart_meter import views as smart_meter_views

urlpatterns = [
    # Smart Tank endpoints
    path('login/', smart_tank_views.ThirdPartyLoginView.as_view(), name='third_party_login'),
    path('company-details/', smart_tank_views.ThirdPartyCompanyDetailsView.as_view(), name='third_party_company_details'),

    # Smart Meter endpoints (add new module endpoints here)
    path('meter-login/', smart_meter_views.SmartMeterLoginView.as_view(), name='smart_meter_login'),
    path('meter-readings/', smart_meter_views.SmartMeterReadingsView.as_view(), name='smart_meter_readings'),

    # ... other patterns
]
```

2. Ensure all views in the new module use the `@extend_schema` decorator with module-based tags:

```python
@extend_schema(tags=["Smart Meter - Authentication"])
class SmartMeterLoginView(APIView):
    @extend_schema(
        summary="Smart Meter User Login",
        description="Authenticate smart meter users and obtain access tokens",
        tags=["Smart Meter - Authentication"]
    )
    def post(self, request):
        # Implementation
        pass
```

**Important**:

-   Use the format `[Module Name] - [Category]` for tags to group endpoints by module
-   All endpoints are at root level (e.g., `/api/v1/third-party/login/`) but grouped by module in documentation

### Customizing Documentation

You can customize the documentation by modifying the `ThirdPartySchemaGenerator` in `thirdparty/schema.py`:

-   **Title**: Change the `title` parameter
-   **Description**: Modify the `description` parameter
-   **Version**: Update the `version` parameter
-   **Filtering Logic**: Modify the `get_endpoints` method

## Security Considerations

-   The documentation endpoints are publicly accessible
-   No sensitive internal endpoints are exposed
-   Authentication requirements are clearly documented
-   Rate limiting is applied to third-party endpoints

## Development Guidelines

### Adding New Third-Party Endpoints

When creating new third-party endpoints:

1. **Use Proper Decorators**: Always use `@extend_schema` with appropriate tags
2. **Follow Naming Conventions**: Use "Third Party - Category" format for tags
3. **Provide Clear Documentation**: Include summary and description
4. **Specify Response Schemas**: Define proper response serializers

Example:

```python
@extend_schema(tags=["Third Party - New Category"])
class NewThirdPartyView(APIView):
    @extend_schema(
        summary="Brief description",
        description="Detailed description of what this endpoint does",
        responses=YourResponseSerializer,
        tags=["Third Party - New Category"]
    )
    def get(self, request):
        # Implementation
        pass
```

### Testing the Documentation

1. Start the Django development server
2. Navigate to the documentation URLs
3. Verify that only third-party endpoints are visible
4. Check that endpoints are properly organized by tags
5. Test the interactive features in Swagger UI

## Troubleshooting

### Common Issues

1. **Endpoints Not Appearing**: Ensure views have proper `@extend_schema` decorators
2. **Wrong Organization**: Check that tags follow the "Third Party - Category" format
3. **Schema Errors**: Verify that response serializers are properly defined
4. **Access Issues**: Confirm that URL patterns are correctly configured

### Debug Mode

To enable debug output in the schema generator, you can add print statements in the `get_endpoints` method of `ThirdPartySchemaGenerator`.

## Comparison with Main API Documentation

| Feature             | Main API Docs       | Third-Party API Docs        |
| ------------------- | ------------------- | --------------------------- |
| **URL**             | `/api/v1/docs/`     | `/api/v1/third-party/docs/` |
| **Scope**           | All endpoints       | Third-party only            |
| **Organization**    | By app modules      | By functionality            |
| **Target Audience** | Internal developers | External partners           |
| **Security**        | Internal access     | Public access               |

## Future Enhancements

Potential improvements to the isolated documentation system:

1. **API Key Documentation**: Enhanced documentation for API key usage
2. **Code Examples**: Auto-generated code examples in multiple languages
3. **Versioning**: Support for multiple API versions
4. **Custom Themes**: Branded documentation themes
5. **Interactive Testing**: Enhanced testing capabilities with sample data

## Support

For questions or issues related to the third-party API documentation:

1. Check this documentation first
2. Review the implementation in `thirdparty/schema.py` and `thirdparty/views.py`
3. Test with the development server
4. Contact the development team for additional support
