"""atg_web URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/2.0/topics/http/urls/
"""
from django.contrib import admin
from django.conf import settings
from django.urls import path, include
from django.conf.urls.static import static

from drf_spectacular.views import SpectacularAPIView, SpectacularRedocView, SpectacularSwaggerView


urlpatterns = [
    # App endpoints
    path('api/v1/', include('backend.urls')),
    path('api/v1/third-party/', include('thirdparty.urls')),
    # Default documentation for internal/existing APIs
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api/v1/docs/', SpectacularSwaggerView.as_view(url_name='schema'),
         name='swagger-ui'),
    path('api/v2/docs/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),


    # Admin
    path('admin/', admin.site.urls),
]

# Serve media in debug mode
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL,
                          document_root=settings.MEDIA_ROOT)
