from django.urls import path
from backend.smart_tank import views as smart_tank_views

urlpatterns = [
    #     path('login/', smart_tank_views.ThirdPartyLoginView.as_view(),
    #          name='login'),
    #     path('company-details/', smart_tank_views.ThirdPartyCompanyDetailsView.as_view(),
    #          name='company_details'),
    #     path('user-details/', smart_tank_views.ThirdPartyUserDetailsView.as_view(),
    #          name='user_details'),
    #     path('site-details/', smart_tank_views.ThirdPartySiteDetailsView.as_view(),
    #          name='site_details'),
    #     path('site-tanks/', smart_tank_views.ThridPartyRetrieveSiteTanksView.as_view(),
    #          name='retrieve_site_tanks'),
]
