from django.shortcuts import render
from rest_framework import (
    status,
    views,
    permissions
)
from .. import utils
from .serializers import ThirdPartyLoginSerializer
from rest_framework_simplejwt.views import TokenObtainPairView
from backend.authentication import serializer
from django.contrib.auth import get_user_model
from drf_spectacular.utils import extend_schema
from django.contrib.auth.signals import user_logged_in
from django.contrib.auth.models import Group, Permission
from backend.companies import serializer as company_serializer
from backend.users import serializer as user_serializer
from backend.sites import serializer as site_serializer
from backend.tanks import serializer as tank_serializer
from .. import models


class ThirdPartyLoginView(TokenObtainPairView):
    '''
    Login logs a user into the system
    '''
    serializer_class = serializer.LoginSerializer

    @extend_schema(responses=serializer.LoginResponseSerializer)
    def post(self, request, *args, **kwargs):
        # check the request body for the keys

        keys = request.data.keys()
        if not 'Email' in keys or request.data['Email'] == '':
            return utils.CustomResponse.Failure(error="Email Field is required")
        elif not 'password' in keys or request.data['password'] == '':
            return utils.CustomResponse.Failure(error="password Field is required")
        # check if the user exist
        try:
            user = get_user_model().objects.get(Email=request.data['Email'])
            if user.is_active == False:
                return utils.CustomResponse.Failure('This user is inactive', status=status.HTTP_403_FORBIDDEN)
        except get_user_model().DoesNotExist:
            return utils.CustomResponse.Failure("user does not exist", status=404)

        try:
            response = super().post(request, *args, **kwargs)
            user_id = response.data.get('user')["id"]
            user = get_user_model().objects.get(pk=user_id)
            if user.is_active == False:
                return utils.CustomResponse.Failure('This user is inactive', status=status.HTTP_403_FORBIDDEN)
            user_logged_in.send(sender=user.__class__,
                                request=request, user=user)
            # get the group name if it is null
            if response.data["user"]['groups'] == None:
                # set the group instance to null
                group_instance = ''
                permission_in_group = []
                response.data['user']['group_permission'] = []
            else:
                group_instance = response.data["user"]['groups']['name']
            #     # using the group name get the permissions in that group
                permission_in_group = Group.objects.get(
                    name=group_instance).permissions.all()
              # clean the data to be Json Serializable
                cleaned_data = serializer.PermissionSerializer(
                    permission_in_group, many=True).data
            #     # append the cleaned data to the response
                response.data['user']['group_permission'] = cleaned_data
            return utils.CustomResponse.Success(response.data)
        except:
            return utils.CustomResponse.Failure('Invalid Login Credentials', status=status.HTTP_401_UNAUTHORIZED)


class ThirdPartyCompanyDetailsView(views.APIView):
    """
    View to get the company details of a third-party user.
    """
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(responses=company_serializer.CompanySerializer)
    def get(self, request, *args, **kwargs):
        # check the request user is a third-party user
        if not request.user.third_party:
            return utils.CustomResponse.Failure(error="This endpoint is only for third-party users", status=status.HTTP_403_FORBIDDEN)
        # check the user company

        if not hasattr(request.user, 'Company'):
            return utils.CustomResponse.Failure(error="User does not belong to any company", status=status.HTTP_404_NOT_FOUND)
        company_details = company_serializer.CompanySerializer(
            request.user.Company).data
        return utils.CustomResponse.Success(data=company_details, status=status.HTTP_200_OK)


class ThirdPartyUserDetailsView(views.APIView):
    """
    View to get the user details of a third-party user.
    """
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(responses=user_serializer.UserSerializer)
    def get(self, request, *args, **kwargs):
        # check the request user is a third-party user
        if not request.user.third_party:
            return utils.CustomResponse.Failure(error="This endpoint is only for third-party users", status=status.HTTP_403_FORBIDDEN)
        user_details = user_serializer.UserSerializer(request.user).data
        return utils.CustomResponse.Success(data=user_details, status=status.HTTP_200_OK)


class ThirdPartySiteDetailsView(views.APIView):
    """
    View to get the site details of a third-party user.
    """
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(responses=user_serializer.SiteSerializer)
    def get(self, request, *args, **kwargs):
        # check the request user is a third-party user
        if not request.user.third_party:
            return utils.CustomResponse.Failure(error="This endpoint is only for third-party users", status=status.HTTP_403_FORBIDDEN)
        # check the user site

        if not hasattr(request.user, 'Sites'):
            return utils.CustomResponse.Failure(error="User does not belong to any site", status=status.HTTP_404_NOT_FOUND)

        # filter the sites by user company
        sites_by_company = models.Sites.objects.filter(
            Company__Company_id=request.user.Company.Company_id)
        if not sites_by_company:
            return utils.CustomResponse.Failure(error="No sites found for this company", status=status.HTTP_404_NOT_FOUND)
        # serialize the sites
        site_details = site_serializer.SiteSerializer(
            sites_by_company, many=True).data
        return utils.CustomResponse.Success(data=site_details, status=status.HTTP_200_OK)


class ThridPartyRetrieveSiteTanksView(views.APIView):
    """
    View to get the tanks of a site for a third-party user.
    """
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(responses=tank_serializer.TankSerializer)
    def get(self, request, *args, **kwargs):
        # check the request user is a third-party user
        if not request.user.third_party:
            return utils.CustomResponse.Failure(error="This endpoint is only for third-party users", status=status.HTTP_403_FORBIDDEN)

        site_id = kwargs.get('site_id')
        if not site_id:
            return utils.CustomResponse.Failure(error="Site ID is required", status=status.HTTP_400_BAD_REQUEST)

        third_party_tanks = models.Tanks.objects.filter(
            Site__Site_id=site_id, Site__Company__Company_id=request.user.Company.Company_id)
        if not third_party_tanks:
            return utils.CustomResponse.Failure(error="No tanks found for this site", status=status.HTTP_404_NOT_FOUND)

        tank_serializer_data = tank_serializer.TankSerializer(
            third_party_tanks, many=True).data
        return utils.CustomResponse.Success(data=tank_serializer_data, status=status.HTTP_200_OK)
