# Third Party API Module

This module provides isolated API endpoints and documentation specifically designed for third-party integrations and external service providers.

## Overview

The third-party module is organized by individual modules (e.g., `smart_tank`, `smart_meter`, etc.), with each module containing its own endpoints that are grouped together in the Swagger documentation.

## Module Structure

```
thirdparty/
├── __init__.py
├── README.md                 # This file
├── urls.py                   # Main URL configuration
├── views.py                  # Shared views and schema views
├── schema.py                 # Custom schema generator
├── authentication.py        # API key authentication
├── throttles.py             # Rate limiting
├── utils.py                 # Utility functions
├── models.py                # Shared models
└── smart_tank/              # Example module
    ├── __init__.py
    ├── urls.py              # Module-specific URLs
    ├── views.py             # Module-specific views
    ├── serializers.py       # Module-specific serializers
    ├── models.py            # Module-specific models
    └── ...
```

## Adding a New Third-Party Module

Follow these steps to add a new third-party module (e.g., `smart_meter`):

### 1. Create Module Directory Structure

```bash
mkdir thirdparty/smart_meter
touch thirdparty/smart_meter/__init__.py
touch thirdparty/smart_meter/urls.py
touch thirdparty/smart_meter/views.py
touch thirdparty/smart_meter/serializers.py
touch thirdparty/smart_meter/models.py
```

### 2. Configure Module URLs

Create `thirdparty/smart_meter/urls.py`:

```python
from django.urls import path
from . import views

urlpatterns = [
    path('login/', views.SmartMeterLoginView.as_view(), name='smart_meter_login'),
    path('readings/', views.SmartMeterReadingsView.as_view(), name='smart_meter_readings'),
    # Add more endpoints as needed
]
```

### 3. Create Module Views with Proper Tagging

In `thirdparty/smart_meter/views.py`, use the module name as the tag prefix:

```python
from rest_framework.views import APIView
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema
from thirdparty.authentication import APIKeyAuthentication
from thirdparty.throttles import APIKeyRateThrottle

@extend_schema(tags=["Smart Meter - Authentication"])
class SmartMeterLoginView(APIView):
    authentication_classes = [APIKeyAuthentication]
    throttle_classes = [APIKeyRateThrottle]
    
    @extend_schema(
        summary="Smart Meter User Login",
        description="Authenticate smart meter users and obtain access tokens",
        tags=["Smart Meter - Authentication"]
    )
    def post(self, request):
        # Implementation here
        pass

@extend_schema(tags=["Smart Meter - Readings"])
class SmartMeterReadingsView(APIView):
    authentication_classes = [APIKeyAuthentication]
    throttle_classes = [APIKeyRateThrottle]
    
    @extend_schema(
        summary="Get Meter Readings",
        description="Retrieve meter readings for authenticated users",
        tags=["Smart Meter - Readings"]
    )
    def get(self, request):
        # Implementation here
        pass
```

### 4. Register Module in Main URLs

Add your module to `thirdparty/urls.py`:

```python
urlpatterns = [
    path('smart-tanks/', include('thirdparty.smart_tank.urls')),
    path('smart-meters/', include('thirdparty.smart_meter.urls')),  # Add this line
    # ... other patterns
]
```

### 5. Tag Naming Convention

**IMPORTANT**: Use the following tag naming convention to group endpoints by module:

- **Format**: `[Module Name] - [Category]`
- **Examples**:
  - `Smart Tank - Authentication`
  - `Smart Tank - Company`
  - `Smart Tank - Tanks`
  - `Smart Meter - Authentication`
  - `Smart Meter - Readings`
  - `Smart Solar - Authentication`
  - `Smart Solar - Panels`

This ensures that all endpoints from the same module are grouped together in the Swagger documentation.

## Documentation Access

### Swagger UI (Interactive)
```
http://your-domain.com/api/v1/third-party/docs/
```

### ReDoc (Alternative Format)
```
http://your-domain.com/api/v1/third-party/redoc/
```

### Raw OpenAPI Schema
```
http://your-domain.com/api/v1/third-party/third-party-schema/
```

## Best Practices

### 1. Authentication & Security
- Always use `APIKeyAuthentication` for third-party endpoints
- Apply `APIKeyRateThrottle` to prevent abuse
- Validate third-party user permissions appropriately

### 2. Documentation
- Use `@extend_schema` decorator on all views and methods
- Provide clear `summary` and `description` for each endpoint
- Define proper response serializers
- Follow the module-based tagging convention

### 3. Error Handling
- Use consistent error response format
- Provide meaningful error messages
- Include appropriate HTTP status codes

### 4. Testing
- Write comprehensive tests for all endpoints
- Test authentication and authorization
- Verify rate limiting functionality
- Test error scenarios

## Example Implementation

See the `smart_tank` module for a complete example implementation that demonstrates:
- Proper URL configuration
- Module-based tagging
- Authentication and throttling
- Comprehensive documentation
- Error handling

## Troubleshooting

### Endpoints Not Appearing in Documentation
1. Ensure views have `@extend_schema` decorators
2. Check that tags follow the module naming convention
3. Verify URL patterns are correctly configured
4. Restart the development server

### Wrong Grouping in Swagger
1. Check tag naming follows `[Module Name] - [Category]` format
2. Ensure both class and method decorators use the same tag format
3. Verify no typos in tag names

### Authentication Issues
1. Confirm `APIKeyAuthentication` is properly configured
2. Check API key is valid and active
3. Verify user has third-party permissions

## Support

For questions or issues:
1. Review this README and existing module implementations
2. Check the main documentation in `THIRD_PARTY_API_DOCS.md`
3. Test with the development server
4. Contact the development team for additional support
