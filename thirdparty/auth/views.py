from django.shortcuts import render
from rest_framework import (
    status,
    views,
    permissions
)
from backend import utils
from rest_framework_simplejwt.views import TokenObtainPairView
from backend.authentication import serializer
from django.contrib.auth import get_user_model
from drf_spectacular.utils import extend_schema, OpenApiParameter
from django.contrib.auth.signals import user_logged_in
from django.contrib.auth.models import Group, Permission
from .. import models


@extend_schema(tags=["Third Party - Authentication"])
class ThirdPartyLoginView(TokenObtainPairView):
    '''
    Login logs a user into the system for third-party access
    '''
    serializer_class = serializer.LoginSerializer

    @extend_schema(
        summary="Third Party User Login",
        description="Authenticate third-party users and obtain access tokens",
        responses=serializer.LoginResponseSerializer,
        tags=["Third Party - Authentication"]
    )
    def post(self, request, *args, **kwargs):
        # check the request body for the keys

        keys = request.data.keys()
        if not 'Email' in keys or request.data['Email'] == '':
            return utils.CustomResponse.Failure(error="Email Field is required")
        elif not 'password' in keys or request.data['password'] == '':
            return utils.CustomResponse.Failure(error="password Field is required")
        # check if the user exist
        try:
            user = get_user_model().objects.get(Email=request.data['Email'])
            if user.is_active == False:
                return utils.CustomResponse.Failure('This user is inactive', status=status.HTTP_403_FORBIDDEN)
        except get_user_model().DoesNotExist:
            return utils.CustomResponse.Failure("user does not exist", status=404)

        # check if the user is a third-party user
        if not user.third_party:
            return utils.CustomResponse.Failure('This user is not a third-party user', status=status.HTTP_403_FORBIDDEN)

        try:
            response = super().post(request, *args, **kwargs)
            if response.status_code == 200:
                # get the user group permission
                user_groups = user.groups.all()
                group_permissions = []
                for group in user_groups:
                    permissions = group.permissions.all()
                    for permission in permissions:
                        group_permissions.append(permission.codename)

                # remove duplicates
                cleaned_data = list(set(group_permissions))

                # add the group permission to the response
                response.data['user']['group_permission'] = cleaned_data
            return utils.CustomResponse.Success(response.data)
        except:
            return utils.CustomResponse.Failure('Invalid Login Credentials', status=status.HTTP_401_UNAUTHORIZED)
