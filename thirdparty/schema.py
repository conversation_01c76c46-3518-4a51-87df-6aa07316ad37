import re
from drf_spectacular.generators import SchemaGenerator


class ThirdPartyGroupedSchemaGenerator(SchemaGenerator):
    """
    Custom schema generator for Smarteye third-party APIs.
    - Filters paths to only include third-party endpoints.
    - Groups endpoints under tags based on the module name in the URL.
    """

    def __init__(self, *args, **kwargs):
        kwargs.setdefault('title', 'Smarteye Third Party API Documentation')
        kwargs.setdefault(
            'description',
            'API documentation for Smarteye third-party integrations and external services.'
        )
        kwargs.setdefault('version', '1.0.0')
        super().__init__(*args, **kwargs)

    def get_endpoints(self, request):
        all_endpoints = super().get_endpoints(request)
        grouped_endpoints = {}

        for path, (view, data) in all_endpoints.items():
            if path.startswith('/api/v1/third-party/'):
                # Extract module name after '/third-party/'
                match = re.search(r'/third-party/([^/]+)/?', path)
                if match:
                    module_name = match.group(1)
                    tag = module_name.replace(
                        '_', ' ').replace('-', ' ').title()
                else:
                    tag = "Third Party"

                # Assign tag only if not already set
                if "tags" not in data or not data["tags"]:
                    data["tags"] = [tag]

                grouped_endpoints[path] = (view, data)

        return grouped_endpoints

    def get_schema(self, request=None, public=False):
        schema = super().get_schema(request=request, public=public)

        if 'info' in schema:
            schema['info'].update({
                'title': 'Smarteye Third Party API Documentation',
                'description': 'This documentation covers endpoints specifically designed for '
                               'external third-party system integrations.',
                'version': '1.0.0',
            })

        if 'paths' in schema:
            schema['paths'] = {
                path: data for path, data in schema['paths'].items()
                if path.startswith('/api/v1/third-party/')
            }

        return schema
