# from rest_framework import serializers

# class ThirdPartyLoginSerializer(serializers.Serializer):
#     """
#     Serializer for third-party login.
#     """
#     Email = serializers.CharField(max_length=150, required=True)
#     password = serializers.CharField(max_length=128, write_only=True, required=True)

#     def validate_Email(self, value):
#         """
#         Validate the email format.
#         """
#         if not value or '@' not in value:
#             raise serializers.ValidationError("Invalid email format.")
#         return value
