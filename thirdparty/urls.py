# thirdparty/urls.py

from django.urls import path
from drf_spectacular.views import SpectacularSwaggerView, SpectacularRedocView
# from rest_framework.views import APIView
# from rest_framework.response import Response
# from drf_spectacular.renderers import OpenApiJsonRenderer, OpenApiYamlRenderer
# from drf_spectacular.generators import SchemaGenerator
# from rest_framework.permissions import AllowAny

from django.urls import include
# from . import urls as thirdparty_urls
from .views import ThirdPartyHealthCheckView, ThirdPartySpectacularAPIView, ThirdPartySpectacularSwaggerView
from .schema import ThirdPartyGroupedSchemaGenerator


# Custom Swagger and ReDoc views that use our custom schema generator
class ThirdPartySwaggerView(SpectacularSwaggerView):
    def get_generator_class(self):
        return ThirdPartyGroupedSchemaGenerator


class ThirdPartyRedocView(SpectacularRedocView):
    def get_generator_class(self):
        return ThirdPartyGroupedSchemaGenerator


urlpatterns = [
    # Authentication endpoints
    path('auth/', include('thirdparty.auth.urls')),

    # Company endpoints
    path('company/', include('thirdparty.company.urls')),

    # User endpoints
    path('user/', include('thirdparty.user.urls')),

    # Sites endpoints
    path('sites/', include('thirdparty.sites.urls')),

    # Tanks endpoints
    path('tanks/', include('thirdparty.tanks.urls')),

    # Other endpoints
    path('health-check/', ThirdPartyHealthCheckView.as_view(),
         name='third-party-health-check'),

    # Documentation endpoints
    path('third-party-schema/', ThirdPartySpectacularAPIView.as_view(),
         name='third-party-schema'),
    path('docs/', ThirdPartySpectacularSwaggerView.as_view(),
         name='third-party-docs'),
    path('redoc/', ThirdPartyRedocView.as_view(url_name='third-party-schema'),
         name='third-party-redoc'),
]
