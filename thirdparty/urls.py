# thirdparty/urls.py

from django.urls import path
from drf_spectacular.views import SpectacularSwaggerView, SpectacularRedocView
from rest_framework.views import APIView
from rest_framework.response import Response
from drf_spectacular.renderers import OpenApiJsonRenderer, OpenApiYamlRenderer
from drf_spectacular.generators import SchemaGenerator
from rest_framework.permissions import AllowAny

from . import urls as thirdparty_urls
from .views import ThirdPartyExampleView, ThirdPartySpectacularAPIView, ThirdPartySpectacularSwaggerView
from .schema import ThirdPartySchemaGenerator
from .smart_tank import views as smart_tank_views


# Custom Swagger and ReDoc views that use our custom schema generator
class ThirdPartySwaggerView(SpectacularSwaggerView):
    def get_generator_class(self):
        return ThirdPartySchemaGenerator


class ThirdPartyRedocView(SpectacularRedocView):
    def get_generator_class(self):
        return ThirdPartySchemaGenerator


urlpatterns = [
    # Smart Tank endpoints (grouped in documentation but at root level)
    path('login/', smart_tank_views.ThirdPartyLoginView.as_view(),
         name='third_party_login'),
    path('company-details/', smart_tank_views.ThirdPartyCompanyDetailsView.as_view(),
         name='third_party_company_details'),
    path('user-details/', smart_tank_views.ThirdPartyUserDetailsView.as_view(),
         name='third_party_user_details'),
    path('site-details/', smart_tank_views.ThirdPartySiteDetailsView.as_view(),
         name='third_party_site_details'),
    path('site-tanks/<int:site_id>/', smart_tank_views.ThridPartyRetrieveSiteTanksView.as_view(),
         name='third_party_retrieve_site_tanks'),

    # Other endpoints
    path('example/', ThirdPartyExampleView.as_view(), name='third-party-example'),

    # Documentation endpoints
    path('third-party-schema/', ThirdPartySpectacularAPIView.as_view(),
         name='third-party-schema'),
    path('docs/', ThirdPartySpectacularSwaggerView.as_view(),
         name='third-party-docs'),
    path('redoc/', ThirdPartyRedocView.as_view(url_name='third-party-schema'),
         name='third-party-redoc'),
]
