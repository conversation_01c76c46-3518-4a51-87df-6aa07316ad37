from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from drf_spectacular.renderers import OpenApi<PERSON>son<PERSON><PERSON>er, OpenApiYamlRenderer
from drf_spectacular.utils import extend_schema
from drf_spectacular.views import SpectacularAPIView, SpectacularSwaggerView

from thirdparty.authentication import APIKeyAuthentication
from thirdparty.throttles import APIKeyRateThrottle
from thirdparty.utils import flatten_multiple_apps
from thirdparty.schema import ThirdPartyGroupedSchemaGenerator


class ThirdPartySpectacularAPIView(SpectacularAPIView):
    """
    Custom API view that generates OpenAPI schema specifically for third-party endpoints.
    This provides isolated documentation for external integrations.
    """

    def get(self, request, *args, **kwargs):
        """Override to use our custom schema generator."""
        generator = ThirdPartyGroupedSchemaGenerator()
        schema = generator.get_schema(request=request, public=True)
        return Response(schema)


class ThirdPartySpectacularSwaggerView(SpectacularSwaggerView):
    """
    Custom Swagger UI view for third-party API documentation.
    This provides an interactive documentation interface for external integrations.
    """
    url_name = 'third-party-schema'
    template_name = 'drf_spectacular/swagger_ui.html'


@extend_schema(tags=["Third Party - Health Check"])
class ThirdPartyHealthCheckView(APIView):
    """
    Health check endpoint for third-party API testing and demonstration.
    """
    authentication_classes = [APIKeyAuthentication]
    throttle_classes = [APIKeyRateThrottle]
    permission_classes = [AllowAny]

    @extend_schema(
        summary="Third Party Health Check Endpoint",
        description="A simple health check endpoint for testing third-party API access",
        responses={200: {"type": "object", "properties": {
            "message": {"type": "string"}}}},
        tags=["Third Party - Health Check"]
    )
    def get(self, request):
        return Response({"message": "Ok"})
